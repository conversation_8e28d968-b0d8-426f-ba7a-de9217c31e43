{"name": "sues_yjsycb", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/pro-components": "2.8.10", "@craco/craco": "^7.1.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "ahooks": "^3.9.0", "antd": "5.26.7", "axios": "^1.11.0", "craco-less": "^3.0.1", "css-in-js": "^1.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"less": "^4.4.0", "less-loader": "^12.3.0"}}