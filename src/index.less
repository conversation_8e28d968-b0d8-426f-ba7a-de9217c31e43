.App {
  min-height: 100vh;
  background: url("./components/icon/background.png") top center/cover no-repeat;
  background-size: 100% 360px;
  background-repeat: no-repeat;
  background-color: #f5f5f5;
  position: relative;
}

.background-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 360px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
  z-index: 1;
  pointer-events: none;
  
  .main-title {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  
  .sub-title {
    font-size: 18px;
    font-weight: 400;
    max-width: 800px;
    line-height: 1.6;
  }
}

.main-content-wrapper {
  min-height: 100vh;
}


.tab-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.tab-placeholder h3 {
  color: #333;
  margin-bottom: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .back-page-container {
    padding: 20px 10px;
  }

  .page-title {
    font-size: 24px;
    letter-spacing: 4px;
  }

  .content-wrapper {
    padding: 20px;
  }
}
