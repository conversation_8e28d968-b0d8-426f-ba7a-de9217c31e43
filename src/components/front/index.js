import { useState } from "react";
import { useLocation } from "react-router-dom";
import "./index.less";
import InfoDeclare from "./InfoDeclare";
import InfoApply from "./InfoApply";
import InfoService from "./InfoService";
import SideMenu from "./SideMenu";

function FrontIndex() {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    { id: 0, title: '信息化申报"一件事"', path: "/" },
    { id: 1, title: '信息化资源申请"一件事"', path: "/" },
    { id: 2, title: '信息化服务"一窗口"', path: "/" },
  ];

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
  };
  const renderContent = (activeTab) => {
    switch (activeTab) {
      case 0:
        return <InfoDeclare />;
      case 1:
        return <InfoApply />;
      case 2:
        return <InfoService />;
      default:
        return null;
    }
  };

  return (
    <div className="front-app">
      <div className="tab-navigation">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? "active" : ""}`}
            onClick={() => handleTabClick(tab.id)}
          >
            {tab.title}
          </button>
        ))}
      </div>
      <div className="main-content">
        <div className="tab-content">
          <div className="header-section">
            <span className="page-title">事项清单</span>
            <span className="page-subtitle">LIST OF MATTERS</span>
          </div>
          {renderContent(activeTab)}
        </div>
      </div>
      <SideMenu />
    </div>
  );
}

export default FrontIndex;
