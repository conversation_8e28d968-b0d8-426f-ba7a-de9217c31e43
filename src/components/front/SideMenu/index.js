import React from 'react';
import { useNavigate } from 'react-router-dom';
import './SideMenu.less';
import questionIcon from '../../icon/question.png'
import searchIcon from '../../icon/search.png'
import sriteIcon from '../../icon/write.png'
import systemIcon from '../../icon/system.png'
import totopIcon from '../../icon/totop.png'

function SideMenu() {
  const navigate = useNavigate();

  const menuItems = [
    {
      id: 1,
      icon: <img src={questionIcon} alt="常见问题" />,
      text: '常见问题',
      path: '/question'
    },
    {
      id: 2,
      icon: <img src={searchIcon} alt="进度查询" />,
      text: '进度查询',
      path: '/yjsycbback/process-search'
    },
    {
      id: 3,
      icon: <img src={sriteIcon} alt="服务评价" />,
      text: '服务评价',
      path: '/yjsycbback/service-evaluation'
    },
    {
      id: 4,
      icon: <img src={systemIcon} alt="系统报障" />,
      text: '系通报障',
      path: '/yjsycbback/system-complaint'
    },
    {
      id: 5,
      icon: <img src={totopIcon} alt="返回顶部" />,
      text: '返回顶部',
      path: null
    }
  ];

  const handleMenuClick = (item) => {
    if (item.text === '返回顶部') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else if (item.path) {
      navigate(item.path);
    }
  };

  return (
    <div className="side-menu">
      {menuItems.map((item) => (
        <div 
          key={item.id} 
          className="menu-item"
          onClick={() => handleMenuClick(item)}
        >
          <div className="menu-icon">{item.icon}</div>
          <div className="menu-text">{item.text}</div>
        </div>
      ))}
    </div>
  );
}

export default SideMenu;
