.side-menu {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-60%);
  display: flex;
  flex-direction: column;
  gap: 2px;
  z-index: 1000;
  background-color: rgba(9, 76, 147, 0.6); /* 使用 rgba 替代 opacity */
}

.menu-item {
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color:#fff
}

.menu-item:hover {
  transform: translateX(-5px);
}


.menu-icon {
  font-size: 24px;
  margin-bottom: 4px;
  
  img {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}

.menu-text {
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}


/* 响应式设计 */
@media (max-width: 768px) {
  .side-menu {
    right: 10px;
  }
  
  .menu-item {
    width: 60px;
    height: 60px;
  }
  
  .menu-icon {
    font-size: 20px;
  }
  
  .menu-text {
    font-size: 10px;
  }
}
