import React, { useState } from "react";
import { Checkbox, Collapse, message } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { getServiceType } from "../../../api/info";
import { useRequest } from "ahooks";
import doubleUp from "../../icon/double-up.svg";
import "./InfoApply.less";

function InfoApply() {
  const [serviceLists, setServiceLists] = useState({});
  const [selectedItems, setSelectedItems] = useState([]);
  const [activeKeys, setActiveKeys] = useState([]);

  const {
    data: serviceTypes,
    loading,
    error,
  } = useRequest(getServiceType, {
    defaultParams: [{ ssfl: "信息化资源" }],
    onSuccess: (res) => {
      console.log("获取服务类型成功:", res);
      if (res?.success && res?.list) {
        setServiceLists(res.list); // 直接设置为数组
        const allItems = res.list.map((item) => item.ywlxid);
        setSelectedItems((prev) =>
          prev.filter((item) => allItems.includes(item))
        );
      }
    },
  });

  const handleItemToggle = (item) => {
    const itemId = item.ywlxid;
    setSelectedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter((i) => i !== itemId)
        : [...prev, itemId]
    );
  };

  const handleGuideClick = (e, itemId) => {
    e.stopPropagation();
    setActiveKeys((prev) =>
      prev.includes(itemId)
        ? prev.filter((key) => key !== itemId)
        : [...prev, itemId]
    );
  };

  const panelStyle = {
    borderRadius: 0,
    border: 0,
  };

  if (loading) {
    return <div className="info-apply">加载中...</div>;
  }

  if (error) {
    return <div className="info-apply">加载失败，请重试</div>;
  }

  const handleApply = () => {
    if (selectedItems?.length > 0) {
      const ids = selectedItems?.join(",");
      window.open(
        `/default/base/workflow/start.jsp?process=com.sudytech.work.shgcd.yjsycb.xxhzysq&ywlxids=${ids}`
      );
    } else {
      message.info("请至少选择1个事项");
    }
  };

  return (
    <div className="info-apply">
      <div className="service-header">
        <span className="service-description">
          以下事项属于全程网办，可根据实际信息化资源要求选择多事项一次办
        </span>
      </div>
      <div className="service-list">
        <Collapse
          bordered={false}
          style={{ backgroundColor: "transparent" }}
          activeKey={activeKeys}
          onChange={() => {}}
          items={serviceLists.map((item) => ({
            key: item.ywlxid || item.ywlxmc,
            label: (
              <div className="service-item">
                <div className="item-content">
                  <Checkbox
                    checked={selectedItems.includes(item.ywlxid || item.ywlxmc)}
                    onChange={() => handleItemToggle(item)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  <span className="item-name">{item.ywlxmc}</span>
                </div>
                <button
                  className="guide-button"
                  onClick={(e) =>
                    handleGuideClick(e, item.ywlxid || item.ywlxmc)
                  }
                >
                  办事指南{" "}
                  {activeKeys.includes(item.ywlxid || item.ywlxmc) ? (
                    <UpOutlined />
                  ) : (
                    <DownOutlined />
                  )}
                </button>
              </div>
            ),
            children: (
              <div className="details-content">
                <div
                  dangerouslySetInnerHTML={{
                    __html: item.bszn || "暂无办事指南",
                  }}
                />
                <div
                  className="collapse-button"
                  onClick={(e) =>
                    handleGuideClick(e, item.ywlxid || item.ywlxmc)
                  }
                >
                  <img className="double-up" src={doubleUp} alt="收起" />
                </div>
              </div>
            ),
            showArrow: false,
            style: panelStyle,
          }))}
        />
      </div>
      <div className="apply-footer">
        <span className="selected-count">
          已选择{selectedItems.length}个事项
        </span>
        <button className="submit-button" onClick={handleApply}>
          立即办理
        </button>
      </div>
    </div>
  );
}

export default InfoApply;
