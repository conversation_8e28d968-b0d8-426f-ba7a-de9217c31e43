.info-apply {
  .service-header {
    color: #f09017;
    font-size: 14px;
    margin: 0;
    padding: 12px 0;
  }

  .service-list {
    background-color: #f5faff;
    display: flex;
    flex-direction: column;
    gap: 1px;
    border: 1px solid #d6e3f0;

    .service-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-content {
        display: flex;
        align-items: center;
        gap: 15px;
        flex: 1;

        .item-name {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }

      .guide-button {
        background: #d3e8ff;
        font-weight: 700;
        color: #1973cc;
        border: none;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;
      }
    }
  }

  .apply-footer {
    background-color: #f5faff;
    border: 1px solid #d6e3f0;
    border-top: 0;
    display: flex;
    gap: 20px;
    align-items: center;
    padding: 16px 12px;

    .submit-button {
      font-weight: 700;
      background: linear-gradient(90deg, #22acea 0%, #176dc9 100%);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      cursor: pointer;
      transition: background 0.3s ease;
    }
  }

  .selected-count {
    color: #ff9500;
    font-size: 14px;
    font-weight: 500;
  }

  .details-content {
    color: #000;
    background-color: #e8f1fa;
    line-height: 1.6;
  }

  .collapse-button {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .double-up {
    width: 14px;
    height: 14px;
  }

  .ant-collapse-item {
    border-bottom: 1px solid #d6e3f0 !important;

    &:last-child {
      border-bottom: none !important;
    }
  }

  .ant-collapse-header {
    cursor: default !important;
    padding: 8px 12px;
  }

  .ant-collapse-content {
    background-color: #e8f1fa !important;
    border-top: 1px solid #d6e3f0 !important;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .service-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .item-content {
      width: 100%;
    }

    .apply-footer {
      flex-direction: column;
      gap: 15px;
      align-items: stretch;
    }

    .submit-button {
      width: 100%;
    }
  }
}
