
.project-collapse {
  background-color:'red !important';

  .project-card {
    overflow: hidden;
    transition: all 0.3s ease;
    border-bottom: 1px solid #a8d0fd;

    &:last-child {
      border-bottom: none;
    }
  }

  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .project-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .project-name {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .project-tag {
    background: #FFF7ED;
    color: #F09017;
    border: 1px solid #F09017;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .project-actions {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .guide-button {
    background: #d3e8ff;
    font-weight: 700;
    color: #1973cc;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer !important;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: #b8d9ff;
    }
  }

  .apply-button {
    font-weight: 700;
    background: linear-gradient(90deg, #22acea 0%, #176dc9 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .project-details {
    background: #fff;
    border-radius: 20px;
    padding: 12px 24px;
    margin-bottom: 12px;
  }

  .guide-section {
    h4 {
      color: #333;
      font-size: 16px;
      margin-bottom: 15px;
      font-weight: 600;
    }
  }

  .details-content {
    color: #666;
    line-height: 1.6;
  }

  .detail-item {
    margin-bottom: 8px;
    font-size: 14px;

    strong {
      color: #333;
    }
  }

  .collapse-button {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    img {
      width: 16px;
      height: 16px;
    }
  }

  .double-up {
    width: 14px;
    height: 14px;
  }

  // Antd 组件样式覆盖
  .ant-collapse-item {
    border: 0 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .ant-collapse-content {
    background-color: #fff !important;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .ant-collapse-header {
    cursor: default !important;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .project-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
    }

    .project-actions {
      align-self: stretch;
      justify-content: space-between;
    }
  }
}
