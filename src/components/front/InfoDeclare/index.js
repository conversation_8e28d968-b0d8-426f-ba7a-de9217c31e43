import React, { useState, useEffect } from "react";
import { Tag, Collapse } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { getServiceType } from "../../../api/info";
import { useRequest } from "ahooks";
import doubleUp from '../../icon/double-up.svg'
import "./InfoDeclare.less";

function InfoDeclare() {
  const [projects, setProjects] = useState([]);
  const [activeKeys, setActiveKeys] = useState([]);

  const { loading, error } = useRequest(getServiceType, {
    defaultParams: [{ ssfl: "信息化申报" }],
    onSuccess: (res) => {
      console.log("获取服务类型成功:", res);
      if (res?.success && res?.list) {
        setProjects(res?.list);
      }
    },
  });

  const handleGuideClick = (e, itemId) => {
    e.stopPropagation(); // 阻止事件冒泡
    setActiveKeys((prev) =>
      prev.includes(itemId)
        ? prev.filter((key) => key !== itemId)
        : [...prev, itemId]
    );
  };

  const panelStyle = {
    marginBottom: "24px",
    backgroundColor: "#F5FAFF",
    borderRadius: "10px",
  };

  if (loading) {
    return <div className="project-list">加载中...</div>;
  }

  return (
    <div className="project-collapse">
      <Collapse
        bordered={false}
        style={{ backgroundColor: "#fff" }}
        activeKey={activeKeys}
        onChange={() => {}} // 禁用默认的点击展开
        items={projects.map((item) => ({
          key: item.ywlxid,
          label: (
            <div className="project-header">
              <div className="project-info">
                <h3 className="project-name">{item.ywlxmc}</h3>
                <span className="project-tag">{item.ssflmc}</span>
              </div>
              <div className="project-actions">
                <button
                  className="guide-button"
                  onClick={(e) => handleGuideClick(e, item.ywlxid)}
                >
                  办事指南 {activeKeys.includes(item.ywlxid) ? <UpOutlined /> : <DownOutlined />}
                </button>
                <button className="apply-button" onClick={()=>window.open(`/default/base/workflow/start.jsp?process=com.sudytech.xxxtsb.xxxtsb&ywlxmc=${item.ywlxmc}`)}>立即办理</button>
              </div>
            </div>
          ),
          children: (
            <div className="details-content">
              <div dangerouslySetInnerHTML={{ __html: item.bszn }} />
              <div 
                className="collapse-button"
                onClick={(e) => handleGuideClick(e, item.ywlxid)}
              >
                <img className="double-up" src={doubleUp} alt="收起" />
              </div>
            </div>
          ),
          showArrow: false,
          style: panelStyle,
        }))}
      />
    </div>
  );
}

export default InfoDeclare;
