import React, { useState } from "react";
import { Collapse, Checkbox, message } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { getServiceType } from "../../../api/info";
import { useRequest } from "ahooks";
import doubleUp from "../../icon/double-up.svg";
import "./InfoService.less";

function InfoService() {
  const [serviceLists, setServiceLists] = useState({});
  const [selectedItems, setSelectedItems] = useState([]);
  const [activeKeys, setActiveKeys] = useState([]);

  const { loading, error } = useRequest(getServiceType, {
    defaultParams: [{ ssfl: "信息化服务" }],
    onSuccess: (res) => {
      console.log("res+++++:", res);
      // 按fwflmc合并数据
      const mergedData = res.list.reduce((acc, current) => {
        const key = current.fwflmc;
        if (acc[key]) {
          acc[key].push(current);
        } else {
          acc[key] = [current];
        }
        return acc;
      }, {});
      setServiceLists(mergedData);
      console.log("mergedData===", mergedData);
    },
  });

  const handleItemToggle = (item) => {
    setSelectedItems((prev) =>
      prev.includes(item.ywlxid)
        ? prev.filter((i) => i !== item.ywlxid)
        : [...prev, item.ywlxid]
    );
  };

  const handleGuideClick = (e, itemId) => {
    e.stopPropagation();
    setActiveKeys((prev) =>
      prev.includes(itemId)
        ? prev.filter((key) => key !== itemId)
        : [...prev, itemId]
    );
  };

  const panelStyle = {
    borderRadius: 0,
    border: 0,
  };

  if (loading) {
    return <div className="info-service">加载中...</div>;
  }

  if (error) {
    return <div className="info-service">加载失败，请重试</div>;
  }

  const handleApply = () => {
    if (selectedItems?.length > 0) {
      const ids = selectedItems?.join(",");
      window.open(
        `/default/base/workflow/start.jsp?process=com.sudytech.work.shgcd.yjsycb.xxhfwsq&ywlxids=${ids}`
      );
    } else {
      message.info("请至少选择1个事项");
    }
  };

  return (
    <div className="info-service">
      <div className="service-header">
        <span className="service-description">
          以下事项属于全程网办，可根据实际信息化要求选择多事项一次办
        </span>
      </div>
      <div className="service-list">
        {Object.entries(serviceLists).map(([categoryName, items]) => (
          <div key={categoryName} className="category-layout">
            <div className="category-sidebar">
              <h3 className="category-title">{categoryName}</h3>
            </div>
            <div className="category-content">
              <Collapse
                bordered={false}
                style={{ backgroundColor: "transparent" }}
                activeKey={activeKeys}
                onChange={() => {}}
                items={items.map((item) => ({
                  key: item.ywlxid,
                  label: (
                    <div className="service-item">
                      <div className="item-content">
                        <Checkbox
                          checked={selectedItems.includes(item.ywlxid)}
                          onChange={() => handleItemToggle(item)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <span className="item-name">{item.ywlxmc}</span>
                      </div>
                      <button
                        className="guide-button"
                        onClick={(e) => handleGuideClick(e, item.ywlxid)}
                      >
                        办事指南{" "}
                        {activeKeys.includes(item.ywlxid) ? (
                          <UpOutlined />
                        ) : (
                          <DownOutlined />
                        )}
                      </button>
                    </div>
                  ),
                  children: (
                    <div className="details-content">
                      <div dangerouslySetInnerHTML={{ __html: item.bszn }} />
                      <div
                        className="collapse-button"
                        onClick={(e) => handleGuideClick(e, item.ywlxid)}
                      >
                        <img className="double-up" src={doubleUp} alt="收起" />
                      </div>
                    </div>
                  ),
                  showArrow: false,
                  style: panelStyle,
                }))}
              />
            </div>
          </div>
        ))}
      </div>

      <div className="service-footer">
        <span className="selected-count">
          已选择{selectedItems.length}个事项
        </span>
        <button className="submit-button" onClick={handleApply}>
          立即办理
        </button>
      </div>
    </div>
  );
}

export default InfoService;
