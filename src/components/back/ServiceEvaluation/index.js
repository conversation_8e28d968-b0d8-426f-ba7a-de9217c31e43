import React, { useState, useEffect, useRef } from "react";
import { Tabs, Button, Modal, Rate, Input, message } from "antd";
import { useRequest } from "ahooks";
import { ProTable } from "@ant-design/pro-components";
import "./ServiceEvaluation.less";
import { getEvaluationInfo, addEvaluation } from "../../../api/info";
import { SearchOutlined } from "@ant-design/icons";

const { TabPane } = Tabs;
const { TextArea } = Input;

function ServiceEvaluation() {
  const [activeTab, setActiveTab] = useState("pending");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");
  const [currentRecord, setCurrentRecord] = useState(null);
  const pendingref = useRef();
  const evaluatedref = useRef();
  //待评价
  const pendingProcessData = async (params, sort, filter) => {
    try {
      console.log("请求参数:", params, "当前tab:", activeTab);
      const res = await getEvaluationInfo({
        lx: 0,
        begin: (params.current - 1) * params.pageSize || 0,
        length: params.pageSize || 10,
      });
      return {
        data: res?.list || [],
        success: true,
        total: res?.page?.count,
      };
    } catch (error) {
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };
  //已评价
  const evaluatedProcessData = async (params, sort, filter) => {
    try {
      console.log("请求参数:", params, "当前tab:", activeTab);
      const res = await getEvaluationInfo({
        lx: 1,
        begin: (params.current - 1) * params.pageSize || 0,
        length: params.pageSize || 10,
      });
      return {
        data: res?.list || [],
        success: true,
        total: res?.page?.count,
      };
    } catch (error) {
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  //新增服务评价
  const { run: _addEvaluation } = useRequest(addEvaluation, {
    manual: true,
    onSuccess: (res) => {
      if (res?.success) {
        message.success("评价提交成功");
        setIsModalVisible(false);
        pendingref?.current?.reload();
        evaluatedref?.current?.reload();
      } else {
        message.error(res.message);
      }
    },
    onError: () => {
      message.error("提交失败");
    },
  });

  // 根据来源设置正确的tab
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  const pendingColumns = [
    {
      title: "序号",
      dataIndex: "serialNumber",
      key: "serialNumber",
      width: 60,
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "事务名称",
      dataIndex: "swmc",
      key: "swmc",
      width: 180,
      render: (text) => <span style={{ color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "业务编号",
      dataIndex: "ywbh",
      key: "ywbh",
      width: 120,
    },
    {
      title: "事务来源",
      dataIndex: "swly",
      key: "swly",
      width: 150,
    },
    {
      title: "发起时间",
      dataIndex: "fqsj",
      key: "fqsj",
      width: 120,
    },
    {
      title: "办结时间",
      dataIndex: "bjsj",
      key: "bjsj",
      width: 120,
    },
    {
      title: "整体用时",
      dataIndex: "ztys",
      key: "ztys",
      width: 100,
    },
    {
      title: "操作",
      key: "action",
      width: 80,
      align: "center",
      render: (_, record) => (
        <Button
          type="link"
          className="evaluate-button"
          onClick={() => handleEvaluate(record)}
        >
          评价
        </Button>
      ),
    },
  ];

  const evaluatedColumns = [
    {
      title: "序号",
      dataIndex: "serialNumber",
      key: "serialNumber",
      width: 60,
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "事务名称",
      dataIndex: "swmc",
      key: "swmc",
      width: 180,
      render: (text) => <span style={{ color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "业务编号",
      dataIndex: "ywbh",
      key: "ywbh",
      width: 120,
    },
    {
      title: "事务来源",
      dataIndex: "swly",
      key: "swly",
      width: 150,
    },
    {
      title: "发起时间",
      dataIndex: "fqsj",
      key: "fqsj",
      width: 120,
    },
    {
      title: "办结时间",
      dataIndex: "bjsj",
      key: "bjsj",
      width: 120,
    },
    {
      title: "整体用时",
      dataIndex: "ztys",
      key: "ztys",
      width: 100,
    },
    {
      title: "评价得分",
      dataIndex: "pjdf",
      key: "pjdf",
      width: 100,
    },
    {
      title: "查看",
      key: "view",
      width: 60,
      align: "center",
      render: (_, record) =>
        record?.url ? (
          <Button
            icon={<SearchOutlined />}
            type="link"
            className="evaluate-button"
            onClick={() => window.open(record.url)}
          />
        ) : null,
    },
  ];

  const handleEvaluate = (record) => {
    setCurrentRecord(record);
    setIsModalVisible(true);
    setRating(0);
    setComment("");
  };

  const handleModalOk = async () => {
    if (!rating) {
      message.error("请选择评价星级");
      return;
    }
    if (!comment) {
      message.error("请填写评价内容");
      return;
    }
    _addEvaluation({
      processInstId: currentRecord.processInstId,
      pjdf: rating,
      pjnr: comment,
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <div className="service-evaluation">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        className="process-tabs"
      >
        <TabPane tab="待评价" key="pending">
          <ProTable
            actionRef={pendingref}
            key="pending"
            columns={pendingColumns}
            request={pendingProcessData}
            size="middle"
            options={false}
            search={false}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
            }}
            rowKey={(record, index) => record.key || record.ywbh || index}
          />
        </TabPane>
        <TabPane tab="已评价" key="evaluated">
          <ProTable
            actionRef={evaluatedref}
            key="evaluated"
            columns={evaluatedColumns}
            request={evaluatedProcessData}
            size="middle"
            options={false}
            search={false}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
            }}
            rowKey={(record, index) => record.key || record.ywbh || index}
          />
        </TabPane>
      </Tabs>

      <Modal
        title="我要评价"
        visible={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="提交"
        cancelText="取消"
        className="evaluation-modal"
        width={500}
      >
        <div className="modal-content">
          <div className="rating-section">
            <span className="rating-label">整体评价：</span>
            <Rate
              value={rating}
              onChange={setRating}
              style={{ fontSize: "24px" }}
            />
          </div>

          <div className="comment-section">
            <TextArea
              placeholder="请输入您的评价内容"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ServiceEvaluation;
