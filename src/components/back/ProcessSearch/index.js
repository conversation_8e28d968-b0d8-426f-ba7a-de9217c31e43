import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { ProTable } from "@ant-design/pro-components";
import { getProcessInfo } from "../../../api/info";
import { SearchOutlined } from "@ant-design/icons";
import "./ProcessSearch.less";

const { TabPane } = Tabs;

function ProcessSearch() {
  const [activeTab, setActiveTab] = useState("processing");

  // 统一的请求函数
  const fetchProcessData = async (params, sort, filter) => {
    try {
      console.log("请求参数:", params, "当前tab:", activeTab);
      const res = await getProcessInfo({
        lx: activeTab === "processing" ? 0 : 1,
        begin: (params.current - 1) * params.pageSize || 0,
        length: params.pageSize || 10,
      });
      return {
        data: res?.list || [],
        success: true,
        total: res?.page?.count,
      };
    } catch (error) {
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  const ProcessingColumns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 60,
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "事务名称",
      dataIndex: "swmc",
      key: "swmc",
      width: 180,
      render: (text) => <span style={{ color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "业务编号",
      dataIndex: "ywbh",
      key: "ywbh",
      width: 120,
    },
    {
      title: "事务来源",
      dataIndex: "swly",
      key: "swly",
      width: 150,
    },
    {
      title: "当前所在节点",
      dataIndex: "dqszjd",
      key: "dqszjd",
      width: 120,
    },
    {
      title: "发起时间",
      dataIndex: "fqsj",
      key: "fqsj",
      width: 120,
    },
    {
      title: "查看",
      key: "view",
      width: 60,
      align: "center",
      render: (_, record) =>
        record?.url ? (
          <Button
            icon={<SearchOutlined />}
            type="link"
            className="evaluate-button"
            onClick={() => window.open(record.url)}
          />
        ) : null,
    },
  ];

  const DoneColumns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 60,
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "事务名称",
      dataIndex: "swmc",
      key: "swmc",
      width: 240,
      render: (text) => <span style={{ color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "业务编号",
      dataIndex: "ywbh",
      key: "ywbh",
      width: 180,
    },
    {
      title: "事务来源",
      dataIndex: "swly",
      key: "swly",
      width: 150,
    },
    {
      title: "发起时间",
      dataIndex: "fqsj",
      key: "fqsj",
      width: 120,
    },
    {
      title: "查看",
      key: "view",
      width: 60,
      align: "center",
      render: (_, record) =>
        record?.url ? (
          <Button
            icon={<SearchOutlined />}
            type="link"
            className="evaluate-button"
            onClick={() => window.open(record.url)}
          />
        ) : null,
    },
  ];

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  return (
    <div className="process-search">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        className="process-tabs"
      >
        <TabPane tab="办理中" key="processing">
          <ProTable
            key="processing"
            columns={ProcessingColumns}
            request={fetchProcessData}
            size="middle"
            options={false}
            search={false}
            pagination={{
              defaultPageSize:10,
              showSizeChanger: true,
            }}
            rowKey={(record, index) => record.key || record.ywbh || index}
          />
        </TabPane>
        <TabPane tab="已办结" key="finished">
          <ProTable
            key="finished"
            columns={DoneColumns}
            request={fetchProcessData}
            size="middle"
            options={false}
            search={false}
            pagination={{
              defaultPageSize:10,
              showSizeChanger: true,
            }}
            rowKey={(record, index) => record.key || record.ywbh || index}
          />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default ProcessSearch;
