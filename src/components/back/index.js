import React, { useState, useEffect } from "react";
import { Tabs } from "antd";
import { useLocation } from "react-router-dom";
import ProcessSearch from "./ProcessSearch";
import ServiceEvaluation from "./ServiceEvaluation";
import SystemComplaint from "./SystemComplaint";
import "./index.less";

function BackIndex() {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    { id: 0, title: '进度查询', path: "/yjsycbback/process-search" },
    { id: 1, title: '服务评价', path: "/yjsycbback/service-evaluation" },
    { id: 2, title: '系统报障', path: "/yjsycbback/system-complaint" },
  ];

  // 根据路由设置默认 activeTab
  useEffect(() => {
    const currentPath = location.pathname;
    const matchedTab = tabs.find(tab => tab.path === currentPath);
    if (matchedTab) {
      setActiveTab(matchedTab.id);
    } else if (currentPath === "/faq") {
      // 如果是 /faq 路由，默认显示第一个 tab
      setActiveTab(0);
    }
  }, [location.pathname]);

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
  };

  const renderContent = (activeTab) => {
    switch (activeTab) {
      case 0:
        return <ProcessSearch />;
      case 1:
        return <ServiceEvaluation />;
      case 2:
        return <SystemComplaint />;
      default:
        return null;
    }
  };

  return (
    <div className="back-app">
      <div className="tab-navigation">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? "active" : ""}`}
            onClick={() => handleTabClick(tab.id)}
          >
            {tab.title}
          </button>
        ))}
      </div>
      <div className="main-content">
        <div className="tab-content">{renderContent(activeTab)}</div>
      </div>
    </div>
  );
}

export default BackIndex;
