.back-app {
  padding-top: 360px;
}
.tab-navigation {
  display: flex;
  justify-content: center;
  background: transparent;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  color: #908F8F;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2px;
  min-width: 180px;
  background:transparent
}

.tab-button.active {
  color: #2E7AC3;
}

.main-content {
  padding: 0 20px 40px 20px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-section {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .page-title {
    color: #094c93;
    font-size: 32px;
    font-weight: 700;
  }
  .page-subtitle {
    color: #094c93;
    font-size: 14px;
    font-weight: 700;
    opacity: 0.2;
  }
}

.tab-content {
  width: 90%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tab-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.tab-placeholder h2 {
  color: #333;
  margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-button {
    font-size: 12px;
    padding: 12px 10px;
  }

  .page-title {
    font-size: 32px;
  }
}
