.system-complaint {
  padding: 20px;
}

.complaint-tabs .ant-tabs-nav {
  margin-bottom: 20px;
}

.complaint-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.complaint-form-section {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.complaint-form {
  max-width: 800px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-item-half {
  flex: 1;
}

.complaint-form .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

.complaint-form .ant-input,
.complaint-form .ant-select-selector {
  border-radius: 6px;
}

.complaint-form .ant-input:disabled {
  background: #f5f5f5;
  color: #666;
}

.submit-button {
  background: #1890ff;
  border-color: #1890ff;
  border-radius: 6px;
  height: 40px;
  padding: 0 30px;
  font-size: 16px;
  font-weight: 500;
}

.submit-button:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.complaint-list-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.status-tab {
  padding: 8px 16px;
  background: #f5f5f5;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-tab.active {
  background: #1890ff;
  color: white;
}

.status-tab:hover:not(.active) {
  background: #e6f7ff;
  color: #1890ff;
}

.complaint-table {
  background: white;
  border-radius: 8px;
}

.complaint-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
}

.complaint-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.complaint-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.system-link {
  color: #1890ff;
  text-decoration: none;
}

.system-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.view-button {
  padding: 0;
  font-size: 16px;
  color: #1890ff;
}

.view-button:hover {
  color: #40a9ff;
}

.tab-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.tab-placeholder h3 {
  color: #333;
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-complaint {
    padding: 10px;
  }
  
  .complaint-form-section {
    padding: 16px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .status-tabs {
    flex-direction: column;
    gap: 10px;
  }
  
  .complaint-table .ant-table-thead > tr > th,
  .complaint-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .section-title {
    font-size: 16px;
  }
  
  .submit-button {
    width: 100%;
    height: 44px;
  }
  
  .complaint-tabs .ant-tabs-tab {
    font-size: 14px;
  }
}
