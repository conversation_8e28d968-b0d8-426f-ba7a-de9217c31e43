import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { ProTable } from "@ant-design/pro-components";
import { getSystemInfo } from "../../../api/info";
import { SearchOutlined } from "@ant-design/icons";
import './SystemComplaint.less'

const { TabPane } = Tabs;

function ProcessSearch() {
  const [activeTab, setActiveTab] = useState("unfinished");

  // 统一的请求函数
  const fetchProcessData = async (params, sort, filter) => {
    try {
      console.log("请求参数:", params, "当前tab:", activeTab);
      const res = await getSystemInfo({
        lx: activeTab === "unfinished" ? 0 : 1,
        begin: (params.current - 1) * params.pageSize || 0,
        length: params.pageSize || 10,
      });
      return {
        data: res?.list || [],
        success: true,
        total: res?.page?.count,
      };
    } catch (error) {
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  const UnFinishedColumns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 60,
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "系统名称",
      dataIndex: "xtmc",
      key: "xtmc",
      width: 180,
      render: (text) => <span style={{ color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "问题描述",
      dataIndex: "wtms",
      key: "wtms",
      width: 180,
    },
    {
      title: "报障时间",
      dataIndex: "bzsj",
      key: "bzsj",
      width: 120,
    },
    {
      title: "查看",
      key: "view",
      width: 60,
      align: "center",
      render: (_, record) =>
        record?.url ? (
          <Button
            icon={<SearchOutlined />}
            type="link"
            className="evaluate-button"
            onClick={() => window.open(record.url)}
          />
        ) : null,
    },
  ];

  const FinishedColumns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 60,
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "系统名称",
      dataIndex: "xtmc",
      key: "xtmc",
      width: 180,
      render: (text) => <span style={{ color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "问题描述",
      dataIndex: "wtms",
      key: "wtms",
      width: 180,
    },
    {
      title: "报障时间",
      dataIndex: "bzsj",
      key: "bzsj",
      width: 120,
    },
    {
      title: "解决时间",
      dataIndex: "jjsj",
      key: "jjsj",
      width: 120,
    },
    {
      title: "查看",
      key: "view",
      width: 60,
      align: "center",
      render: (_, record) =>
        record?.url ? (
          <Button
            icon={<SearchOutlined />}
            type="link"
            className="evaluate-button"
            onClick={() => window.open(record.url)}
          />
        ) : null,
    },
  ];

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  return (
    <div className="system-complaint">
      <div className="iframe-container" style={{ marginBottom: "20px" }}>
        <iframe
          // src="/default/base/workflow/start.jsp?process=com.sudytech.work.shgcd.xxxtfksq.xxxtfksq&fromyy=1&appload=0"
          src="http://192.168.110.136:8080/default/base/workflow/start.jsp?process=com.sudytech.work.shgcd.xxxtfksq.xxxtfksq&fromyy=1&appload=0"
          width="100%"
          height="400px"
          frameBorder="0"
          title="系统投诉页面"
          style={{
            border: "1px solid #d9d9d9",
            borderRadius: "6px",
          }}
        />
      </div>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        className="process-tabs"
      >
        <TabPane tab="待解决" key="unfinished">
          <ProTable
            key="unfinished"
            columns={UnFinishedColumns}
            request={fetchProcessData}
            size="middle"
            options={false}
            search={false}
            pagination={{
              defaultPageSize:10,
              showSizeChanger: true,
            }}
            rowKey={(record, index) => record.key || record.ywbh || index}
          />
        </TabPane>
        <TabPane tab="已解决" key="finished">
          <ProTable
            key="finished"
            columns={FinishedColumns}
            request={fetchProcessData}
            size="middle"
            options={false}
            search={false}
            pagination={{
              defaultPageSize:10,
              showSizeChanger: true,
            }}
            rowKey={(record, index) => record.key || record.ywbh || index}
          />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default ProcessSearch;
