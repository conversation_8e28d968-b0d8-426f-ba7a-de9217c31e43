import React from "react";
import ReactDOM from "react-dom/client";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import "./index.less";
import FrontAPP from "./components/front";
import BackApp from "./components/back";
import reportWebVitals from "./reportWebVitals";

// 定义有效路由列表
const VALID_ROUTES = [
  "/faq",
  "/yjsycbback/process-search",
  "/yjsycbback/service-evaluation",
  "/yjsycbback/system-complaint",
  "/yjsycbfront",
];

const isValidRoute = (pathname) => {
  return VALID_ROUTES.includes(pathname);
};

function MainContent() {
  const location = useLocation();

  const renderContent = (pathname) => {
    switch (pathname) {
      case "/faq":
      case "/yjsycbback/process-search":
      case "/yjsycbback/service-evaluation":
      case "/yjsycbback/system-complaint":
        return <BackApp />;
      case "/yjsycbfront":
        return <FrontAPP />;
      default:
        return null;
    }
  };

  // 如果不是有效路由，直接返回 null，不显示任何内容
  if (!isValidRoute(location.pathname)) {
    return null;
  }

  return (
    <div className="main-content-wrapper">
      {location.pathname === "/" ? (
        renderContent(location.pathname)
      ) : (
        <div className="back-page-container">
          <div className="content-wrapper">
            {renderContent(location.pathname)}
          </div>
        </div>
      )}
    </div>
  );
}

function AppContent() {
  const location = useLocation();
  console.log("location===", location);
  return (
    <div className={isValidRoute(location.pathname) ? "App" : ""}>
      {isValidRoute(location.pathname) && (
        <div className="background-text">
          <span className="main-title">信息化服务"一件事"</span>
          <span className="sub-title">
            聚焦信息服务"一件事"统一受理，实现信息化服务事项在线联办
          </span>
        </div>
      )}

      <Routes>
        <Route path="/*" element={<MainContent />} />
      </Routes>
    </div>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(<App />);

reportWebVitals();
