import React from "react";
import ReactDOM from "react-dom/client";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import "./index.less";
import FrontAPP from "./components/front";
import BackApp from "./components/back";
import reportWebVitals from "./reportWebVitals";

function MainContent() {
  const location = useLocation();

  const renderContent = (pathname) => {
    switch (pathname) {
      case "/faq":
      case "/yjsycbback/process-search":
      case "/yjsycbback/service-evaluation":
      case "/yjsycbback/system-complaint":
        return <BackApp />;
      default:
        return <FrontAPP />;
    }
  };

  return (
    <div className="main-content-wrapper">
      {location.pathname === "/" ? (
        renderContent(location.pathname)
      ) : (
        <div className="back-page-container">
          <div className="content-wrapper">
            {renderContent(location.pathname)}
          </div>
        </div>
      )}
    </div>
  );
}

function App() {
  return (
    <Router>
      <div className="App">
        <div className="background-text">
          <span className="main-title">信息化服务"一件事"</span>
          <span className="sub-title">
            聚焦信息服务"一件事"统一受理，实现信息化服务事项在线联办
          </span>
        </div>

        <Routes>
          <Route path="/*" element={<MainContent />} />
        </Routes>
      </div>
    </Router>
  );
}

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(<App />);

reportWebVitals();
