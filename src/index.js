import React from "react";
import ReactDOM from "react-dom/client";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import "./index.less";
import FrontAPP from "./components/front";
import BackApp from "./components/back";
import reportWebVitals from "./reportWebVitals";

const VALID_ROUTES = [
  "/faq",
  "/yjsycbback/process-search",
  "/yjsycbback/service-evaluation",
  "/yjsycbback/system-complaint",
  "/yjsycbfront",
];

const isValidRoute = (pathname) => {
  // 直接匹配
  if (VALID_ROUTES.includes(pathname)) {
    return true;
  }

  // 检查路径是否以有效路由结尾
  if (VALID_ROUTES.some(route => pathname.endsWith(route))) {
    return true;
  }

  // 特殊处理：如果是通过静态文件访问，检查 URL 参数
  if (pathname.includes('/build/') || pathname.includes('index.html')) {
    // 检查 URL 中的 hash 或 search 参数
    const urlParams = new URLSearchParams(window.location.search);
    const route = urlParams.get('route') || window.location.hash.replace('#', '');
    if (route) {
      return VALID_ROUTES.includes(route) || VALID_ROUTES.some(validRoute => route.endsWith(validRoute));
    }
    // 如果没有参数，默认显示（可以根据需要调整）
    return true;
  }

  return false;
};

function MainContent() {
  const location = useLocation();

  const renderContent = (pathname) => {
    if (pathname === "/faq" ||
        pathname.endsWith("/faq") ||
        pathname === "/yjsycbback/process-search" ||
        pathname.endsWith("/yjsycbback/process-search") ||
        pathname === "/yjsycbback/service-evaluation" ||
        pathname.endsWith("/yjsycbback/service-evaluation") ||
        pathname === "/yjsycbback/system-complaint" ||
        pathname.endsWith("/yjsycbback/system-complaint")) {
      return <BackApp />;
    }
    if (pathname === "/yjsycbfront" || pathname.endsWith("/yjsycbfront")) {
      return <FrontAPP />;
    }

    return null;
  };

  if (!isValidRoute(location.pathname)) {
    return null;
  }

  return (
    <div className="main-content-wrapper">
      {location.pathname === "/" ? (
        renderContent(location.pathname)
      ) : (
        <div className="back-page-container">
          <div className="content-wrapper">
            {renderContent(location.pathname)}
          </div>
        </div>
      )}
    </div>
  );
}

function AppContent() {
  const location = useLocation();
  console.log("location===", location);
  return (
    <div className={isValidRoute(location.pathname) ? "App" : ""}>
      {isValidRoute(location.pathname) && (
        <div className="background-text">
          <span className="main-title">信息化服务"一件事"</span>
          <span className="sub-title">
            聚焦信息服务"一件事"统一受理，实现信息化服务事项在线联办
          </span>
        </div>
      )}

      <Routes>
        <Route path="/*" element={<MainContent />} />
      </Routes>
    </div>
  );
}

function App() {
  return (
    <Router basename="/default/work/shgcd/yjsycb/qd">
      <AppContent />
    </Router>
  );
}

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(<App />);

reportWebVitals();
