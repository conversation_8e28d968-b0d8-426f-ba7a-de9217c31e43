import React from "react";
import ReactDOM from "react-dom/client";
import {
  HashRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import "./index.less";
import FrontAPP from "./components/front";
import BackApp from "./components/back";
import reportWebVitals from "./reportWebVitals";

const VALID_ROUTES = [
  "/faq",
  "/yjsycbback/process-search",
  "/yjsycbback/service-evaluation",
  "/yjsycbback/system-complaint",
  "/yjsycbfront",
  "/",
];

const isValidRoute = (pathname) => {
  if (VALID_ROUTES.includes(pathname)) {
    return true;
  }
  return VALID_ROUTES.some((route) => pathname.endsWith(route));
};

function MainContent() {
  const location = useLocation();
  const renderContent = (pathname) => {
    if (
      pathname === "/faq" ||
      pathname.endsWith("/faq") ||
      pathname === "/yjsycbback/process-search" ||
      pathname.endsWith("/yjsycbback/process-search") ||
      pathname === "/yjsycbback/service-evaluation" ||
      pathname.endsWith("/yjsycbback/service-evaluation") ||
      pathname === "/yjsycbback/system-complaint" ||
      pathname.endsWith("/yjsycbback/system-complaint")
    ) {
      return <BackApp />;
    }
    if (
      pathname === "/" ||
      pathname === "/yjsycbfront" ||
      pathname.endsWith("/yjsycbfront")
    ) {
      return <FrontAPP />;
    }

    return null;
  };

  if (!isValidRoute(location.pathname)) {
    return null;
  }

  return (
    <div className="main-content-wrapper">
      {location.pathname === "/" ? (
        renderContent(location.pathname)
      ) : (
        <div className="back-page-container">
          <div className="content-wrapper">
            {renderContent(location.pathname)}
          </div>
        </div>
      )}
    </div>
  );
}

function AppContent() {
  const location = useLocation();
  console.log("location===", location);
  return (
    <div className={isValidRoute(location.pathname) ? "App" : ""}>
      {isValidRoute(location.pathname) && (
        <div className="background-text">
          <span className="main-title">信息化服务"一件事"</span>
          <span className="sub-title">
            聚焦信息服务"一件事"统一受理，实现信息化服务事项在线联办
          </span>
        </div>
      )}

      <Routes>
        <Route path="/*" element={<MainContent />} />
      </Routes>
    </div>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(<App />);

reportWebVitals();
