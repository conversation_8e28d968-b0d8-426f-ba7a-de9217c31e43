import axios from "../utils/axios";

//信息化申报/资源/服务业务类型
const getServiceType = (params) => {
  return axios({
    url: "/default/work/shgcd/yjsycb/ajaxYwlx4Portal.jsp",
    method: "GET",
    params: { ...params },
  });
};

//进度查询
const getProcessInfo = (params) => {
  return axios({
    url: "/default/work/shgcd/yjsycb/ajaxJd4Portal.jsp",
    method: "GET",
    params: { ...params },
  });
};

//服务评价查询
const getEvaluationInfo = (params) => {
  return axios({
    url: "/default/work/shgcd/yjsycb/ajaxFwpj4Portal.jsp",
    method: "GET",
    params: { ...params },
  });
};

//新增服务评价
const addEvaluation = (params) => {
  return axios({
    url: "/default/work/shgcd/yjsycb/ajaxAddFwpj4Portal.jsp",
    method: "GET",
    params: { ...params },
  });
};

//系统报障查询
const getSystemInfo = (params) => {
  return axios({
    url: "/default/work/shgcd/yjsycb/ajaxXtbz4Portal.jsp",
    method: "GET",
    params: { ...params },
  });
};

export { getServiceType, getProcessInfo, getEvaluationInfo, addEvaluation, getSystemInfo };
