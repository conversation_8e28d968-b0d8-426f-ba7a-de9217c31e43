const { createProxyMiddleware } = require('http-proxy-middleware');
const { readFileSync } = require('fs');
const path = require('path');

module.exports = function(app) {
  app.use(
    '/default/work/shgcd/yjsycb',
    createProxyMiddleware({
      target: 'http://192.168.110.136:8080',
      changeOrigin: true,
      pathRewrite: {},
      onProxyReq: function(proxyReq, req, res) {
        try {
          // 读取 cookie.txt 文件
          const cookiePath = path.join(__dirname, 'cookie.txt');
          const cookieContent = readFileSync(cookiePath).toString().trim();
          proxyReq.setHeader('Cookie', cookieContent);
          console.log('设置 Cookie:', cookieContent);
        } catch (error) {
          console.error('读取 cookie.txt 失败:', error);
        }
      },
      onError: function(err, req, res) {
      }
    })
  );
};
