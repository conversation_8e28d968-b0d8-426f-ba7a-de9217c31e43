import axios from 'axios'
import { message } from 'antd'
const service = axios.create({
  baseURL: ``,
  timeout: 20000
})

const err = (error) => {
  if (error.response) {
    switch (error.response.status) {
      case 401: {
        const userInfo = localStorage.getItem('userInfo')
        message.warning('认证失败请重新登录')
        if (userInfo) {
          localStorage.removeItem('userInfo')
          localStorage.removeItem('token')
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
        break
      }
      case 404:
        message.error('未找到资源')
        break
      case 400:
        message.error('错误的请求')
        break
      case 405:
        message.error('Method Not Allowed')
        break
      default:
        message.error(error.response.statusText)
        break
    }
  }
  return Promise.reject(error)
}

service.interceptors.request.use((config) => {
  return config;
}, err);

service.interceptors.response.use((response) => {
  if (response.status === 200) {
    let data = response.data;
    // 处理JSONP格式的响应
    if (typeof data === 'string') {
      try {
        // 提取callback()中的JSON数据
        const jsonStr = data.substring(data.indexOf('(') + 1, data.lastIndexOf(')'));
        data = JSON.parse(jsonStr);
      } catch (error) {
        return Promise.reject(new Error('响应格式错误'));
      }
    } else {
    }

    // 检查业务逻辑成功状态
    if (data && data.success) {
      return Promise.resolve(data);
    } else {
      return Promise.resolve(data); // 仍然返回数据，让组件处理
    }
  }
  return Promise.reject(response);
}, err);


export default service;

