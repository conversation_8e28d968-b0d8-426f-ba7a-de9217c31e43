const CracoLessPlugin = require('craco-less');

module.exports = {
  plugins: [
    {
      plugin: CracoLessPlugin,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            modifyVars: {},
            javascriptEnabled: true,
          },
        },
      },
    },
  ],
  webpack: {
    output: {
      publicPath: "./",
    },
    configure: (webpackConfig, { env, paths }) => {
      webpackConfig.output.publicPath = "auto";
      return webpackConfig;
    },
  },
};