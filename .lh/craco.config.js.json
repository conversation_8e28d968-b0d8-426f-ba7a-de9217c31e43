{"sourceFile": "craco.config.js", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1754391912403, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754392045492, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,8 +15,8 @@\n     },\n   ],\n   webpack: {\n     output: {\n-      publicPath: \"auto\",\n+      publicPath: \"./\",\n     },\n   },\n };\n\\ No newline at end of file\n"}, {"date": 1754392158706, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,5 +18,9 @@\n     output: {\n       publicPath: \"./\",\n     },\n   },\n+  configure: (webpackConfig, { env, paths }) => {\n+    webpackConfig.output.publicPath = \"auto\";\n+    return webpackConfig;\n+  },\n };\n\\ No newline at end of file\n"}, {"date": 1754392316397, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,10 +17,10 @@\n   webpack: {\n     output: {\n       publicPath: \"./\",\n     },\n+    configure: (webpackConfig, { env, paths }) => {\n+      webpackConfig.output.publicPath = \"auto\";\n+      return webpackConfig;\n+    },\n   },\n-  configure: (webpackConfig, { env, paths }) => {\n-    webpackConfig.output.publicPath = \"auto\";\n-    return webpackConfig;\n-  },\n };\n\\ No newline at end of file\n"}, {"date": 1754392435468, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,24 +1,24 @@\n const CracoLessPlugin = require('craco-less');\n \n module.exports = {\n-  plugins: [\n-    {\n-      plugin: CracoLessPlugin,\n-      options: {\n-        lessLoaderOptions: {\n-          lessOptions: {\n-            modifyVars: {},\n-            javascriptEnabled: true,\n-          },\n-        },\n-      },\n-    },\n-  ],\n   webpack: {\n     output: {\n       publicPath: \"./\",\n     },\n+    plugins: [\n+      {\n+        plugin: CracoLessPlugin,\n+        options: {\n+          lessLoaderOptions: {\n+            lessOptions: {\n+              modifyVars: {},\n+              javascriptEnabled: true,\n+            },\n+          },\n+        },\n+      },\n+    ],\n     configure: (webpackConfig, { env, paths }) => {\n       webpackConfig.output.publicPath = \"auto\";\n       return webpackConfig;\n     },\n"}, {"date": 1754392487620, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,24 +1,24 @@\n const CracoLessPlugin = require('craco-less');\n \n module.exports = {\n+  plugins: [\n+    {\n+      plugin: CracoLessPlugin,\n+      options: {\n+        lessLoaderOptions: {\n+          lessOptions: {\n+            modifyVars: {},\n+            javascriptEnabled: true,\n+          },\n+        },\n+      },\n+    },\n+  ],\n   webpack: {\n     output: {\n       publicPath: \"./\",\n     },\n-    plugins: [\n-      {\n-        plugin: CracoLessPlugin,\n-        options: {\n-          lessLoaderOptions: {\n-            lessOptions: {\n-              modifyVars: {},\n-              javascriptEnabled: true,\n-            },\n-          },\n-        },\n-      },\n-    ],\n     configure: (webpackConfig, { env, paths }) => {\n       webpackConfig.output.publicPath = \"auto\";\n       return webpackConfig;\n     },\n"}], "date": 1754391912403, "name": "Commit-0", "content": "const CracoLessPlugin = require('craco-less');\n\nmodule.exports = {\n  plugins: [\n    {\n      plugin: CracoLessPlugin,\n      options: {\n        lessLoaderOptions: {\n          lessOptions: {\n            modifyVars: {},\n            javascriptEnabled: true,\n          },\n        },\n      },\n    },\n  ],\n  webpack: {\n    output: {\n      publicPath: \"auto\",\n    },\n  },\n};"}]}