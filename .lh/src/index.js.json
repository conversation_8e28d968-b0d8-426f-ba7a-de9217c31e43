{"sourceFile": "src/index.js", "activeCommit": 0, "commits": [{"activePatchIndex": 9, "patches": [{"date": 1754392479326, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754392604285, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,8 +27,9 @@\n };\n \n function MainContent() {\n   const location = useLocation();\n+  console.log(\"222222222\");\n \n   const renderContent = (pathname) => {\n     console.log(\"111111111\", pathname);\n     if (pathname === \"/faq\" ||\n"}, {"date": 1754392692654, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,20 +27,22 @@\n };\n \n function MainContent() {\n   const location = useLocation();\n-  console.log(\"222222222\");\n+  console.log(\"222222222\", location.pathname);\n \n   const renderContent = (pathname) => {\n     console.log(\"111111111\", pathname);\n-    if (pathname === \"/faq\" ||\n-        pathname.endsWith(\"/faq\") ||\n-        pathname === \"/yjsycbback/process-search\" ||\n-        pathname.endsWith(\"/yjsycbback/process-search\") ||\n-        pathname === \"/yjsycbback/service-evaluation\" ||\n-        pathname.endsWith(\"/yjsycbback/service-evaluation\") ||\n-        pathname === \"/yjsycbback/system-complaint\" ||\n-        pathname.endsWith(\"/yjsycbback/system-complaint\")) {\n+    if (\n+      pathname === \"/faq\" ||\n+      pathname.endsWith(\"/faq\") ||\n+      pathname === \"/yjsycbback/process-search\" ||\n+      pathname.endsWith(\"/yjsycbback/process-search\") ||\n+      pathname === \"/yjsycbback/service-evaluation\" ||\n+      pathname.endsWith(\"/yjsycbback/service-evaluation\") ||\n+      pathname === \"/yjsycbback/system-complaint\" ||\n+      pathname.endsWith(\"/yjsycbback/system-complaint\")\n+    ) {\n       return <BackApp />;\n     }\n     if (pathname === \"/yjsycbfront\" || pathname.endsWith(\"/yjsycbfront\")) {\n       return <FrontAPP />;\n"}, {"date": 1754392834763, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -92,11 +92,11 @@\n }\n \n function App() {\n   return (\n-    <Router>\n-      <AppContent />\n-    </Router>\n+    // <Router>\n+    <AppContent />\n+    // </Router>\n   );\n }\n \n const root = ReactDOM.createRoot(document.getElementById(\"root\"));\n"}, {"date": 1754392907247, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,8 @@\n import React from \"react\";\n import ReactDOM from \"react-dom/client\";\n import {\n-  BrowserRouter as Router,\n+  HashRouter as Router,\n   Routes,\n   Route,\n   useLocation,\n } from \"react-router-dom\";\n@@ -22,9 +22,9 @@\n const isValidRoute = (pathname) => {\n   if (VALID_ROUTES.includes(pathname)) {\n     return true;\n   }\n-  return VALID_ROUTES.some(route => pathname.endsWith(route));\n+  return VALID_ROUTES.some((route) => pathname.endsWith(route));\n };\n \n function MainContent() {\n   const location = useLocation();\n@@ -92,11 +92,11 @@\n }\n \n function App() {\n   return (\n-    // <Router>\n-    <AppContent />\n-    // </Router>\n+    <Router>\n+      <AppContent />\n+    </Router>\n   );\n }\n \n const root = ReactDOM.createRoot(document.getElementById(\"root\"));\n"}, {"date": 1754393064661, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,8 +16,9 @@\n   \"/yjsycbback/process-search\",\n   \"/yjsycbback/service-evaluation\",\n   \"/yjsycbback/system-complaint\",\n   \"/yjsycbfront\",\n+  \"/\",\n ];\n \n const isValidRoute = (pathname) => {\n   if (VALID_ROUTES.includes(pathname)) {\n"}, {"date": 1754393126386, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,9 +44,13 @@\n       pathname.endsWith(\"/yjsycbback/system-complaint\")\n     ) {\n       return <BackApp />;\n     }\n-    if (pathname === \"/yjsycbfront\" || pathname.endsWith(\"/yjsycbfront\")) {\n+    if (\n+      pathname === \"/\" ||\n+      pathname === \"/yjsycbfront\" ||\n+      pathname.endsWith(\"/yjsycbfront\")\n+    ) {\n       return <FrontAPP />;\n     }\n \n     return null;\n"}, {"date": 1754393185218, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,9 +31,8 @@\n   const location = useLocation();\n   console.log(\"222222222\", location.pathname);\n \n   const renderContent = (pathname) => {\n-    console.log(\"111111111\", pathname);\n     if (\n       pathname === \"/faq\" ||\n       pathname.endsWith(\"/faq\") ||\n       pathname === \"/yjsycbback/process-search\" ||\n"}, {"date": 1754393194187, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -28,10 +28,8 @@\n };\n \n function MainContent() {\n   const location = useLocation();\n-  console.log(\"222222222\", location.pathname);\n-\n   const renderContent = (pathname) => {\n     if (\n       pathname === \"/faq\" ||\n       pathname.endsWith(\"/faq\") ||\n@@ -45,10 +43,10 @@\n       return <BackApp />;\n     }\n     if (\n       pathname === \"/\" ||\n-      pathname === \"/yjsycbfront\" ||\n-      pathname.endsWith(\"/yjsycbfront\")\n+      // pathname === \"/yjsycbfront\" ||\n+      // pathname.endsWith(\"/yjsycbfront\")\n     ) {\n       return <FrontAPP />;\n     }\n \n"}, {"date": 1754393204949, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,10 +43,10 @@\n       return <BackApp />;\n     }\n     if (\n       pathname === \"/\" ||\n-      // pathname === \"/yjsycbfront\" ||\n-      // pathname.endsWith(\"/yjsycbfront\")\n+      pathname === \"/yjsycbfront\" ||\n+      pathname.endsWith(\"/yjsycbfront\")\n     ) {\n       return <FrontAPP />;\n     }\n \n"}], "date": 1754392479326, "name": "Commit-0", "content": "import React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport {\n  BrowserRouter as Router,\n  Routes,\n  Route,\n  useLocation,\n} from \"react-router-dom\";\nimport \"./index.less\";\nimport FrontAPP from \"./components/front\";\nimport BackApp from \"./components/back\";\nimport reportWebVitals from \"./reportWebVitals\";\n\nconst VALID_ROUTES = [\n  \"/faq\",\n  \"/yjsycbback/process-search\",\n  \"/yjsycbback/service-evaluation\",\n  \"/yjsycbback/system-complaint\",\n  \"/yjsycbfront\",\n];\n\nconst isValidRoute = (pathname) => {\n  if (VALID_ROUTES.includes(pathname)) {\n    return true;\n  }\n  return VALID_ROUTES.some(route => pathname.endsWith(route));\n};\n\nfunction MainContent() {\n  const location = useLocation();\n\n  const renderContent = (pathname) => {\n    console.log(\"111111111\", pathname);\n    if (pathname === \"/faq\" ||\n        pathname.endsWith(\"/faq\") ||\n        pathname === \"/yjsycbback/process-search\" ||\n        pathname.endsWith(\"/yjsycbback/process-search\") ||\n        pathname === \"/yjsycbback/service-evaluation\" ||\n        pathname.endsWith(\"/yjsycbback/service-evaluation\") ||\n        pathname === \"/yjsycbback/system-complaint\" ||\n        pathname.endsWith(\"/yjsycbback/system-complaint\")) {\n      return <BackApp />;\n    }\n    if (pathname === \"/yjsycbfront\" || pathname.endsWith(\"/yjsycbfront\")) {\n      return <FrontAPP />;\n    }\n\n    return null;\n  };\n\n  if (!isValidRoute(location.pathname)) {\n    return null;\n  }\n\n  return (\n    <div className=\"main-content-wrapper\">\n      {location.pathname === \"/\" ? (\n        renderContent(location.pathname)\n      ) : (\n        <div className=\"back-page-container\">\n          <div className=\"content-wrapper\">\n            {renderContent(location.pathname)}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction AppContent() {\n  const location = useLocation();\n  console.log(\"location===\", location);\n  return (\n    <div className={isValidRoute(location.pathname) ? \"App\" : \"\"}>\n      {isValidRoute(location.pathname) && (\n        <div className=\"background-text\">\n          <span className=\"main-title\">信息化服务\"一件事\"</span>\n          <span className=\"sub-title\">\n            聚焦信息服务\"一件事\"统一受理，实现信息化服务事项在线联办\n          </span>\n        </div>\n      )}\n\n      <Routes>\n        <Route path=\"/*\" element={<MainContent />} />\n      </Routes>\n    </div>\n  );\n}\n\nfunction App() {\n  return (\n    <Router>\n      <AppContent />\n    </Router>\n  );\n}\n\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render(<App />);\n\nreportWebVitals();\n"}]}