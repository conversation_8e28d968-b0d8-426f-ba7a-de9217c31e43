{"sourceFile": "package.json", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754391620316, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754391620316, "name": "Commit-0", "content": "{\n  \"name\": \"sues_yj<PERSON>cb\",\n  \"version\": \"0.1.0\",\n  \"private\": true,\n  \"dependencies\": {\n    \"@ant-design/pro-components\": \"2.8.10\",\n    \"@craco/craco\": \"^7.1.0\",\n    \"@testing-library/dom\": \"^10.4.1\",\n    \"@testing-library/jest-dom\": \"^6.6.4\",\n    \"@testing-library/react\": \"^16.3.0\",\n    \"@testing-library/user-event\": \"^13.5.0\",\n    \"ahooks\": \"^3.9.0\",\n    \"antd\": \"5.26.7\",\n    \"axios\": \"^1.11.0\",\n    \"craco-less\": \"^3.0.1\",\n    \"css-in-js\": \"^1.1.0\",\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\",\n    \"react-router-dom\": \"^6.26.0\",\n    \"react-scripts\": \"5.0.1\",\n    \"react-toastify\": \"^11.0.5\",\n    \"web-vitals\": \"^2.1.4\"\n  },\n  \"scripts\": {\n    \"start\": \"craco start\",\n    \"build\": \"craco build\",\n    \"test\": \"craco test\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"react-app\",\n      \"react-app/jest\"\n    ]\n  },\n  \"browserslist\": {\n    \"production\": [\n      \">0.2%\",\n      \"not dead\",\n      \"not op_mini all\"\n    ],\n    \"development\": [\n      \"last 1 chrome version\",\n      \"last 1 firefox version\",\n      \"last 1 safari version\"\n    ]\n  },\n  \"devDependencies\": {\n    \"less\": \"^4.4.0\",\n    \"less-loader\": \"^12.3.0\"\n  }\n}\n"}]}